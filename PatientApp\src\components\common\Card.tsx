import React from 'react';
import { View, TouchableOpacity, ViewStyle } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';

interface CardProps {
  children: React.ReactNode;
  onPress?: () => void;
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'small' | 'medium' | 'large';
  margin?: 'none' | 'small' | 'medium' | 'large';
  style?: ViewStyle;
  className?: string;
  animated?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  onPress,
  variant = 'default',
  padding = 'medium',
  margin = 'small',
  style,
  className = '',
  animated = true,
}) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  const handlePressIn = () => {
    if (animated && onPress) {
      scale.value = withSpring(0.98);
    }
  };

  const handlePressOut = () => {
    if (animated && onPress) {
      scale.value = withSpring(1);
    }
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'elevated':
        return 'bg-white shadow-lg border border-secondary-100';
      case 'outlined':
        return 'bg-white border-2 border-secondary-200';
      case 'filled':
        return 'bg-secondary-50 border border-secondary-100';
      default:
        return 'bg-white shadow-md border border-secondary-100';
    }
  };

  const getPaddingClasses = () => {
    switch (padding) {
      case 'none':
        return '';
      case 'small':
        return 'p-3';
      case 'large':
        return 'p-6';
      default:
        return 'p-4';
    }
  };

  const getMarginClasses = () => {
    switch (margin) {
      case 'none':
        return '';
      case 'small':
        return 'mb-2';
      case 'large':
        return 'mb-6';
      default:
        return 'mb-4';
    }
  };

  const cardClasses = `
    rounded-xl
    ${getVariantClasses()}
    ${getPaddingClasses()}
    ${getMarginClasses()}
    ${className}
  `.trim();

  if (onPress) {
    return (
      <Animated.View style={animated ? animatedStyle : undefined}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          className={cardClasses}
          style={style}
          activeOpacity={0.9}
        >
          {children}
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <View className={cardClasses} style={style}>
      {children}
    </View>
  );
};

export default Card;
