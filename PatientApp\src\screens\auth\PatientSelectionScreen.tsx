import React from 'react';
import { View, Text, SafeAreaView, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { useApp } from '../../context/AppContext';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';

type PatientSelectionScreenNavigationProp = StackNavigationProp<RootStackParamList, 'PatientSelection'>;

interface Props {
  navigation: PatientSelectionScreenNavigationProp;
}

const PatientSelectionScreen: React.FC<Props> = ({ navigation }) => {
  const { state, selectPatient } = useApp();

  const handleSelectPatient = (patientId: string) => {
    selectPatient(patientId);
    // Navigation will be handled by AppNavigator based on auth state
  };

  const handleRegisterNewPatient = () => {
    navigation.navigate('RegisterPatient');
  };

  return (
    <SafeAreaView className="flex-1 bg-secondary-50">
      <View className="flex-1">
        {/* Header */}
        <View className="bg-white px-6 py-4 shadow-sm">
          <Text className="text-2xl font-bold text-secondary-800">Select Patient</Text>
          <Text className="text-secondary-500 mt-1">Choose who you're booking for</Text>
        </View>

        <ScrollView className="flex-1 px-6 py-6">
          {/* Existing Patients */}
          <View className="mb-6">
            {state.patients.map((patient) => (
              <Card
                key={patient.id}
                onPress={() => handleSelectPatient(patient.id)}
                variant="elevated"
                style={{ marginBottom: 16 }}
              >
                <View className="flex-row items-center">
                  <View className="h-14 w-14 rounded-full bg-primary-100 items-center justify-center mr-4">
                    <Ionicons 
                      name={patient.gender === 'female' ? 'woman' : 'man'} 
                      size={24} 
                      color="#1e40af" 
                    />
                  </View>
                  
                  <View className="flex-1">
                    <Text className="text-lg font-semibold text-secondary-800">
                      {patient.name}
                    </Text>
                    <Text className="text-secondary-500 capitalize">
                      {patient.age} years • {patient.gender}
                    </Text>
                    {patient.relationship !== 'self' && (
                      <Text className="text-sm text-primary-600 capitalize mt-1">
                        {patient.relationship}
                      </Text>
                    )}
                  </View>
                  
                  <View className="ml-4">
                    <Ionicons name="chevron-forward" size={20} color="#94a3b8" />
                  </View>
                </View>
              </Card>
            ))}
          </View>

          {/* Register New Patient Button */}
          <Button
            title="Register New Patient"
            onPress={handleRegisterNewPatient}
            variant="outline"
            fullWidth
            size="large"
            icon={<Ionicons name="add" size={20} color="#1e40af" />}
          />

          {/* Help Text */}
          <View className="mt-8 p-4 bg-blue-50 rounded-xl">
            <View className="flex-row items-start">
              <Ionicons name="information-circle" size={20} color="#3b82f6" style={{ marginTop: 2, marginRight: 8 }} />
              <View className="flex-1">
                <Text className="text-sm font-medium text-blue-800 mb-1">
                  Multiple Patient Support
                </Text>
                <Text className="text-sm text-blue-600">
                  You can manage appointments for yourself and your family members. 
                  Add new patients anytime from the settings.
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default PatientSelectionScreen;
