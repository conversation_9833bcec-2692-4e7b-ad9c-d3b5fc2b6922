import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, TextInputProps } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  containerStyle?: any;
  inputStyle?: any;
  required?: boolean;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  leftIcon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  inputStyle,
  required = false,
  secureTextEntry,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const handleRightIconPress = () => {
    if (secureTextEntry) {
      togglePasswordVisibility();
    } else if (onRightIconPress) {
      onRightIconPress();
    }
  };

  const getRightIcon = () => {
    if (secureTextEntry) {
      return isPasswordVisible ? 'eye-off-outline' : 'eye-outline';
    }
    return rightIcon;
  };

  return (
    <View className="mb-4" style={containerStyle}>
      {label && (
        <Text className="text-sm font-medium text-secondary-700 mb-2">
          {label}
          {required && <Text className="text-error-500 ml-1">*</Text>}
        </Text>
      )}
      
      <View
        className={`
          flex-row items-center bg-white border rounded-xl px-4 py-3
          ${isFocused ? 'border-primary-500 shadow-sm' : 'border-secondary-200'}
          ${error ? 'border-error-500' : ''}
        `}
      >
        {leftIcon && (
          <Ionicons
            name={leftIcon}
            size={20}
            color={isFocused ? '#3b82f6' : '#64748b'}
            style={{ marginRight: 12 }}
          />
        )}
        
        <TextInput
          className="flex-1 text-base text-secondary-900"
          placeholderTextColor="#94a3b8"
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          style={inputStyle}
          {...props}
        />
        
        {(rightIcon || secureTextEntry) && (
          <TouchableOpacity
            onPress={handleRightIconPress}
            className="ml-2"
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons
              name={getRightIcon() as keyof typeof Ionicons.glyphMap}
              size={20}
              color="#64748b"
            />
          </TouchableOpacity>
        )}
      </View>
      
      {error && (
        <Text className="text-sm text-error-500 mt-1 ml-1">
          {error}
        </Text>
      )}
    </View>
  );
};

export default Input;
