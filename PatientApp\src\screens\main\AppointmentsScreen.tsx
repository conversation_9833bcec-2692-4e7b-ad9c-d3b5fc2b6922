import React, { useState } from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import Card from '../../components/common/Card';

const AppointmentsScreen: React.FC = () => {
  const { state, getSelectedPatient } = useApp();
  const [activeTab, setActiveTab] = useState<'upcoming' | 'completed' | 'cancelled'>('upcoming');
  const selectedPatient = getSelectedPatient();

  const filteredAppointments = state.appointments.filter(
    apt => apt.patientId === selectedPatient?.id && apt.status === activeTab
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'text-primary-600 bg-primary-100';
      case 'completed':
        return 'text-success-600 bg-success-100';
      case 'cancelled':
        return 'text-error-600 bg-error-100';
      default:
        return 'text-secondary-600 bg-secondary-100';
    }
  };

  const tabs = [
    { key: 'upcoming', label: 'Upcoming', count: state.appointments.filter(a => a.status === 'upcoming' && a.patientId === selectedPatient?.id).length },
    { key: 'completed', label: 'Completed', count: state.appointments.filter(a => a.status === 'completed' && a.patientId === selectedPatient?.id).length },
    { key: 'cancelled', label: 'Cancelled', count: state.appointments.filter(a => a.status === 'cancelled' && a.patientId === selectedPatient?.id).length },
  ];

  return (
    <SafeAreaView className="flex-1 bg-secondary-50">
      <View className="flex-1">
        {/* Header */}
        <View className="bg-white px-6 py-4 shadow-sm">
          <Text className="text-2xl font-bold text-secondary-800">Appointments</Text>
          <Text className="text-secondary-500 mt-1">Manage your medical appointments</Text>
        </View>

        {/* Tabs */}
        <View className="bg-white px-6 py-2 border-b border-secondary-100">
          <View className="flex-row">
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.key}
                onPress={() => setActiveTab(tab.key as any)}
                className={`
                  flex-1 py-3 items-center border-b-2
                  ${activeTab === tab.key ? 'border-primary-500' : 'border-transparent'}
                `}
              >
                <View className="flex-row items-center">
                  <Text className={`
                    font-medium
                    ${activeTab === tab.key ? 'text-primary-600' : 'text-secondary-500'}
                  `}>
                    {tab.label}
                  </Text>
                  {tab.count > 0 && (
                    <View className="ml-2 h-5 w-5 rounded-full bg-primary-100 items-center justify-center">
                      <Text className="text-xs font-bold text-primary-600">{tab.count}</Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <ScrollView className="flex-1 px-6 py-6">
          {filteredAppointments.length > 0 ? (
            filteredAppointments.map((appointment) => {
              const doctor = state.doctors.find(d => d.id === appointment.doctorId);
              return (
                <Card key={appointment.id} variant="elevated" className="mb-4">
                  <View className="flex-row items-start">
                    <View className="h-14 w-14 rounded-full bg-primary-100 items-center justify-center mr-4">
                      <Text className="text-primary-600 font-bold text-lg">
                        {doctor?.name.split(' ').map(n => n[0]).join('') || 'DR'}
                      </Text>
                    </View>
                    
                    <View className="flex-1">
                      <View className="flex-row items-start justify-between mb-2">
                        <View className="flex-1">
                          <Text className="text-lg font-semibold text-secondary-800">
                            {doctor?.name || 'Doctor Name'}
                          </Text>
                          <Text className="text-secondary-500 capitalize">
                            {doctor?.specialty || 'Specialty'}
                          </Text>
                        </View>
                        <View className={`px-2 py-1 rounded-full ${getStatusColor(appointment.status)}`}>
                          <Text className="text-xs font-medium capitalize">
                            {appointment.status}
                          </Text>
                        </View>
                      </View>
                      
                      <View className="flex-row items-center mb-2">
                        <Ionicons name="calendar-outline" size={16} color="#64748b" />
                        <Text className="text-secondary-600 ml-2">{appointment.date}</Text>
                        <Ionicons name="time-outline" size={16} color="#64748b" style={{ marginLeft: 16 }} />
                        <Text className="text-secondary-600 ml-2">{appointment.time}</Text>
                      </View>
                      
                      <View className="flex-row items-center justify-between">
                        <View className="flex-row items-center">
                          <Ionicons name="card-outline" size={16} color="#64748b" />
                          <Text className="text-secondary-600 ml-2">₹{appointment.amount}</Text>
                          <View className={`ml-2 px-2 py-1 rounded-full ${
                            appointment.paymentStatus === 'paid' ? 'bg-success-100' :
                            appointment.paymentStatus === 'pending' ? 'bg-warning-100' : 'bg-error-100'
                          }`}>
                            <Text className={`text-xs font-medium ${
                              appointment.paymentStatus === 'paid' ? 'text-success-600' :
                              appointment.paymentStatus === 'pending' ? 'text-warning-600' : 'text-error-600'
                            }`}>
                              {appointment.paymentStatus}
                            </Text>
                          </View>
                        </View>
                        
                        {appointment.status === 'upcoming' && (
                          <TouchableOpacity className="flex-row items-center">
                            <Text className="text-primary-600 font-medium mr-1">Reschedule</Text>
                            <Ionicons name="chevron-forward" size={16} color="#3b82f6" />
                          </TouchableOpacity>
                        )}
                      </View>
                      
                      {appointment.notes && (
                        <View className="mt-3 p-3 bg-secondary-50 rounded-lg">
                          <Text className="text-sm text-secondary-600">{appointment.notes}</Text>
                        </View>
                      )}
                    </View>
                  </View>
                </Card>
              );
            })
          ) : (
            <Card variant="outlined" className="items-center py-12">
              <Ionicons 
                name={
                  activeTab === 'upcoming' ? 'calendar-outline' :
                  activeTab === 'completed' ? 'checkmark-circle-outline' : 'close-circle-outline'
                } 
                size={64} 
                color="#cbd5e1" 
              />
              <Text className="text-lg font-medium text-secondary-500 mt-4">
                No {activeTab} appointments
              </Text>
              <Text className="text-secondary-400 text-center mt-2">
                {activeTab === 'upcoming' 
                  ? 'Book your first appointment to get started'
                  : `You don't have any ${activeTab} appointments yet`
                }
              </Text>
            </Card>
          )}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default AppointmentsScreen;
