import React from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { useApp } from '../../context/AppContext';
import Card from '../../components/common/Card';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { state, getSelectedPatient } = useApp();
  const selectedPatient = getSelectedPatient();

  const upcomingAppointments = state.appointments.filter(
    apt => apt.status === 'upcoming' && apt.patientId === selectedPatient?.id
  );

  const recentRecords = state.medicalRecords
    .filter(record => record.patientId === selectedPatient?.id)
    .slice(0, 3);

  const quickActions = [
    {
      id: 'book-appointment',
      title: 'Book Appointment',
      icon: 'calendar',
      color: '#3b82f6',
      onPress: () => navigation.navigate('SpecialtySelection'),
    },
    {
      id: 'medical-records',
      title: 'Medical Records',
      icon: 'document-text',
      color: '#10b981',
      onPress: () => navigation.navigate('Main', { screen: 'Records' }),
    },
    {
      id: 'lab-results',
      title: 'Lab Results',
      icon: 'flask',
      color: '#f59e0b',
      onPress: () => {},
    },
    {
      id: 'prescriptions',
      title: 'Prescriptions',
      icon: 'medical',
      color: '#ef4444',
      onPress: () => {},
    },
  ];

  return (
    <SafeAreaView className="flex-1 bg-secondary-50">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <LinearGradient
          colors={['#1e40af', '#3b82f6']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          className="px-6 py-8 rounded-b-3xl"
        >
          <View className="flex-row items-center justify-between mb-6">
            <View className="flex-1">
              <Text className="text-xl font-bold text-white">
                Hello, {selectedPatient?.name?.split(' ')[0] || 'User'}
              </Text>
              <Text className="text-blue-200 mt-1">How are you feeling today?</Text>
            </View>
            <TouchableOpacity className="h-12 w-12 rounded-full bg-blue-700 items-center justify-center">
              <Ionicons name="person" size={24} color="white" />
            </TouchableOpacity>
          </View>

          {/* Next Appointment Card */}
          {upcomingAppointments.length > 0 && (
            <Card variant="filled" className="bg-blue-700 border-blue-600">
              <View className="flex-row items-center">
                <View className="mr-4">
                  <Ionicons name="calendar" size={32} color="#bfdbfe" />
                </View>
                <View className="flex-1">
                  <Text className="text-blue-200 text-sm">Your next appointment</Text>
                  <Text className="text-white font-semibold text-base">
                    {state.doctors.find(d => d.id === upcomingAppointments[0].doctorId)?.name}
                  </Text>
                  <Text className="text-blue-200 text-sm">
                    {upcomingAppointments[0].date}, {upcomingAppointments[0].time}
                  </Text>
                </View>
              </View>
            </Card>
          )}
        </LinearGradient>

        <View className="px-6 py-6">
          {/* Quick Actions */}
          <View className="mb-8">
            <Text className="text-lg font-bold text-secondary-800 mb-4">Quick Actions</Text>
            <View className="flex-row flex-wrap justify-between">
              {quickActions.map((action) => (
                <TouchableOpacity
                  key={action.id}
                  onPress={action.onPress}
                  className="w-[48%] mb-4"
                >
                  <Card variant="elevated" className="items-center py-6">
                    <View 
                      className="h-14 w-14 rounded-full items-center justify-center mb-3"
                      style={{ backgroundColor: `${action.color}15` }}
                    >
                      <Ionicons name={action.icon as any} size={24} color={action.color} />
                    </View>
                    <Text className="text-sm font-medium text-secondary-700 text-center">
                      {action.title}
                    </Text>
                  </Card>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Health Stats */}
          <View className="mb-8">
            <Text className="text-lg font-bold text-secondary-800 mb-4">Health Overview</Text>
            <View className="flex-row space-x-4">
              <Card variant="elevated" className="flex-1 items-center py-4">
                <Text className="text-2xl font-bold text-primary-600">120/80</Text>
                <Text className="text-sm text-secondary-500">Blood Pressure</Text>
                <Text className="text-xs text-success-600 mt-1">Normal</Text>
              </Card>
              <Card variant="elevated" className="flex-1 items-center py-4">
                <Text className="text-2xl font-bold text-primary-600">72</Text>
                <Text className="text-sm text-secondary-500">Heart Rate</Text>
                <Text className="text-xs text-success-600 mt-1">Good</Text>
              </Card>
              <Card variant="elevated" className="flex-1 items-center py-4">
                <Text className="text-2xl font-bold text-primary-600">98.6°F</Text>
                <Text className="text-sm text-secondary-500">Temperature</Text>
                <Text className="text-xs text-success-600 mt-1">Normal</Text>
              </Card>
            </View>
          </View>

          {/* Recent Activities */}
          <View className="mb-8">
            <View className="flex-row items-center justify-between mb-4">
              <Text className="text-lg font-bold text-secondary-800">Recent Activities</Text>
              <TouchableOpacity>
                <Text className="text-primary-600 font-medium">View All</Text>
              </TouchableOpacity>
            </View>

            {recentRecords.map((record) => (
              <Card key={record.id} variant="elevated" className="mb-3">
                <View className="flex-row items-center">
                  <View className="h-12 w-12 rounded-full bg-primary-100 items-center justify-center mr-4">
                    <Ionicons 
                      name={
                        record.type === 'lab-report' ? 'flask' :
                        record.type === 'prescription' ? 'medical' :
                        record.type === 'scan' ? 'scan' : 'document-text'
                      } 
                      size={20} 
                      color="#1e40af" 
                    />
                  </View>
                  <View className="flex-1">
                    <Text className="font-semibold text-secondary-800">{record.title}</Text>
                    <Text className="text-sm text-secondary-500">{record.doctorName}</Text>
                    <Text className="text-xs text-secondary-400 mt-1">{record.date}</Text>
                  </View>
                  <Ionicons name="chevron-forward" size={16} color="#94a3b8" />
                </View>
              </Card>
            ))}

            {recentRecords.length === 0 && (
              <Card variant="outlined" className="items-center py-8">
                <Ionicons name="document-text-outline" size={48} color="#cbd5e1" />
                <Text className="text-secondary-500 mt-2">No recent activities</Text>
              </Card>
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default HomeScreen;
