import { Patient, Doctor, Appointment, MedicalRecord, Specialty, TimeSlot } from '../types';

export const samplePatients: Patient[] = [
  {
    id: 'P001',
    name: '<PERSON><PERSON>',
    age: 32,
    gender: 'male',
    phone: '+919876543210',
    email: '<EMAIL>',
    address: '123 MG Road, Bangalore, Karnataka 560001',
    relationship: 'self',
    dateOfBirth: '1992-03-15',
  },
  {
    id: 'P002',
    name: '<PERSON><PERSON>',
    age: 28,
    gender: 'female',
    phone: '+919876543210',
    email: '<EMAIL>',
    address: '123 MG Road, Bangalore, Karnataka 560001',
    relationship: 'spouse',
    dateOfBirth: '1996-07-22',
  },
];

export const sampleSpecialties: Specialty[] = [
  {
    id: 'S001',
    name: 'Cardiology',
    description: 'Heart related issues',
    icon: 'heart',
    doctorCount: 15,
  },
  {
    id: 'S002',
    name: '<PERSON>mat<PERSON>',
    description: 'Skin, hair and nails',
    icon: 'user',
    doctorCount: 12,
  },
  {
    id: 'S003',
    name: 'Orthopedics',
    description: 'Bones and joints',
    icon: 'clipboard',
    doctorCount: 18,
  },
  {
    id: 'S004',
    name: 'Neurology',
    description: 'Brain and nervous system',
    icon: 'globe',
    doctorCount: 8,
  },
  {
    id: 'S005',
    name: 'Pediatrics',
    description: 'Child healthcare',
    icon: 'baby',
    doctorCount: 10,
  },
  {
    id: 'S006',
    name: 'Gynecology',
    description: 'Women\'s health',
    icon: 'female',
    doctorCount: 14,
  },
];

export const sampleTimeSlots: TimeSlot[] = [
  { id: 'T001', time: '09:00 AM', date: '2024-06-15', available: true },
  { id: 'T002', time: '09:30 AM', date: '2024-06-15', available: true },
  { id: 'T003', time: '10:00 AM', date: '2024-06-15', available: false },
  { id: 'T004', time: '10:30 AM', date: '2024-06-15', available: true },
  { id: 'T005', time: '11:00 AM', date: '2024-06-15', available: true },
  { id: 'T006', time: '11:30 AM', date: '2024-06-15', available: true },
  { id: 'T007', time: '04:00 PM', date: '2024-06-15', available: true },
  { id: 'T008', time: '04:30 PM', date: '2024-06-15', available: true },
  { id: 'T009', time: '05:00 PM', date: '2024-06-15', available: true },
];

export const sampleDoctors: Doctor[] = [
  {
    id: 'D001',
    name: 'Dr. Anil Kumar',
    specialty: 'Cardiology',
    experience: 15,
    rating: 4.9,
    reviews: 120,
    consultationFee: 800,
    availableSlots: sampleTimeSlots,
  },
  {
    id: 'D002',
    name: 'Dr. Suresh Kumar',
    specialty: 'Cardiology',
    experience: 10,
    rating: 4.2,
    reviews: 85,
    consultationFee: 600,
    availableSlots: sampleTimeSlots,
  },
  {
    id: 'D003',
    name: 'Dr. Priya Patel',
    specialty: 'Dermatology',
    experience: 8,
    rating: 4.7,
    reviews: 95,
    consultationFee: 700,
    availableSlots: sampleTimeSlots,
  },
  {
    id: 'D004',
    name: 'Dr. Rajesh Singh',
    specialty: 'Orthopedics',
    experience: 12,
    rating: 4.5,
    reviews: 110,
    consultationFee: 900,
    availableSlots: sampleTimeSlots,
  },
];

export const sampleAppointments: Appointment[] = [
  {
    id: 'A001',
    patientId: 'P001',
    doctorId: 'D001',
    date: '2024-06-16',
    time: '10:30 AM',
    status: 'upcoming',
    type: 'consultation',
    amount: 1003,
    paymentStatus: 'paid',
  },
  {
    id: 'A002',
    patientId: 'P001',
    doctorId: 'D001',
    date: '2024-06-08',
    time: '02:00 PM',
    status: 'completed',
    type: 'consultation',
    amount: 1003,
    paymentStatus: 'paid',
    notes: 'Regular checkup completed. All vitals normal.',
  },
];

export const sampleMedicalRecords: MedicalRecord[] = [
  {
    id: 'MR001',
    patientId: 'P001',
    type: 'lab-report',
    title: 'Blood Test Report',
    description: 'Complete blood count and lipid profile',
    date: '2024-06-10',
    doctorName: 'Dr. Anil Kumar',
    attachments: ['blood_test_report.pdf'],
  },
  {
    id: 'MR002',
    patientId: 'P001',
    type: 'prescription',
    title: 'Cardiac Medication',
    description: 'Prescribed medication for blood pressure management',
    date: '2024-06-08',
    doctorName: 'Dr. Anil Kumar',
  },
  {
    id: 'MR003',
    patientId: 'P001',
    type: 'scan',
    title: 'ECG Report',
    description: 'Electrocardiogram showing normal heart rhythm',
    date: '2024-06-05',
    doctorName: 'Dr. Anil Kumar',
    attachments: ['ecg_report.pdf'],
  },
];
