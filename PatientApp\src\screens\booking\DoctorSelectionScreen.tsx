import React from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { useApp } from '../../context/AppContext';
import Card from '../../components/common/Card';

type DoctorSelectionScreenNavigationProp = StackNavigationProp<RootStackParamList, 'DoctorSelection'>;
type DoctorSelectionScreenRouteProp = RouteProp<RootStackParamList, 'DoctorSelection'>;

interface Props {
  navigation: DoctorSelectionScreenNavigationProp;
  route: DoctorSelectionScreenRouteProp;
}

const DoctorSelectionScreen: React.FC<Props> = ({ navigation, route }) => {
  const { specialtyId } = route.params;
  const { state } = useApp();
  
  const specialty = state.specialties.find(s => s.id === specialtyId);
  const doctors = state.doctors.filter(d => d.specialty === specialty?.name);

  const handleDoctorSelect = (doctorId: string) => {
    navigation.navigate('DateTimeSelection', { doctorId });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Ionicons
        key={index}
        name={index < Math.floor(rating) ? 'star' : index < rating ? 'star-half' : 'star-outline'}
        size={16}
        color="#fbbf24"
      />
    ));
  };

  return (
    <SafeAreaView className="flex-1 bg-secondary-50">
      <View className="flex-1">
        {/* Header */}
        <View className="bg-white px-6 py-4 shadow-sm">
          <View className="flex-row items-center">
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              className="mr-4"
            >
              <Ionicons name="arrow-back" size={24} color="#374151" />
            </TouchableOpacity>
            <View className="flex-1">
              <Text className="text-xl font-bold text-secondary-800">Select Doctor</Text>
              <Text className="text-secondary-500">{specialty?.name} specialists</Text>
            </View>
          </View>
        </View>

        <ScrollView className="flex-1 px-6 py-6">
          {doctors.map((doctor) => (
            <Card
              key={doctor.id}
              onPress={() => handleDoctorSelect(doctor.id)}
              variant="elevated"
              className="mb-4"
            >
              <View className="flex-row">
                <View className="h-20 w-20 rounded-full bg-primary-100 items-center justify-center mr-4">
                  <Text className="text-primary-600 font-bold text-xl">
                    {doctor.name.split(' ').map(n => n[0]).join('')}
                  </Text>
                </View>
                
                <View className="flex-1">
                  <Text className="text-lg font-semibold text-secondary-800">
                    {doctor.name}
                  </Text>
                  <Text className="text-secondary-500 capitalize">
                    {doctor.specialty} • {doctor.experience} years exp.
                  </Text>
                  
                  <View className="flex-row items-center mt-2">
                    <View className="flex-row mr-2">
                      {renderStars(doctor.rating)}
                    </View>
                    <Text className="text-sm text-secondary-600">
                      {doctor.rating} ({doctor.reviews} reviews)
                    </Text>
                  </View>
                  
                  <View className="flex-row items-center justify-between mt-3">
                    <Text className="text-lg font-bold text-primary-600">
                      ₹{doctor.consultationFee}
                    </Text>
                    <View className="flex-row items-center">
                      <Ionicons name="time-outline" size={16} color="#10b981" />
                      <Text className="text-success-600 text-sm ml-1">Available today</Text>
                    </View>
                  </View>
                </View>
              </View>
            </Card>
          ))}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default DoctorSelectionScreen;
