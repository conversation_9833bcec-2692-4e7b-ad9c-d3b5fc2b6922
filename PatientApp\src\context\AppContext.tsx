import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState, User, Patient, Appointment, MedicalRecord, Doctor, Specialty } from '../types';
import { samplePatients, sampleAppointments, sampleMedicalRecords, sampleDoctors, sampleSpecialties } from '../data/sampleData';

type AppAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_PATIENTS'; payload: Patient[] }
  | { type: 'ADD_PATIENT'; payload: Patient }
  | { type: 'SELECT_PATIENT'; payload: string }
  | { type: 'SET_APPOINTMENTS'; payload: Appointment[] }
  | { type: 'ADD_APPOINTMENT'; payload: Appointment }
  | { type: 'SET_MEDICAL_RECORDS'; payload: MedicalRecord[] }
  | { type: 'SET_DOCTORS'; payload: Doctor[] }
  | { type: 'SET_SPECIALTIES'; payload: Specialty[] }
  | { type: 'LOGOUT' };

const initialState: AppState = {
  auth: {
    user: null,
    isLoading: true,
    isAuthenticated: false,
  },
  patients: [],
  appointments: [],
  medicalRecords: [],
  doctors: [],
  specialties: [],
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_USER':
      return {
        ...state,
        auth: {
          ...state.auth,
          user: action.payload,
          isAuthenticated: !!action.payload,
          isLoading: false,
        },
      };
    case 'SET_LOADING':
      return {
        ...state,
        auth: {
          ...state.auth,
          isLoading: action.payload,
        },
      };
    case 'SET_PATIENTS':
      return {
        ...state,
        patients: action.payload,
      };
    case 'ADD_PATIENT':
      return {
        ...state,
        patients: [...state.patients, action.payload],
      };
    case 'SELECT_PATIENT':
      return {
        ...state,
        auth: {
          ...state.auth,
          user: state.auth.user ? {
            ...state.auth.user,
            selectedPatientId: action.payload,
          } : null,
        },
      };
    case 'SET_APPOINTMENTS':
      return {
        ...state,
        appointments: action.payload,
      };
    case 'ADD_APPOINTMENT':
      return {
        ...state,
        appointments: [...state.appointments, action.payload],
      };
    case 'SET_MEDICAL_RECORDS':
      return {
        ...state,
        medicalRecords: action.payload,
      };
    case 'SET_DOCTORS':
      return {
        ...state,
        doctors: action.payload,
      };
    case 'SET_SPECIALTIES':
      return {
        ...state,
        specialties: action.payload,
      };
    case 'LOGOUT':
      return {
        ...initialState,
        auth: {
          ...initialState.auth,
          isLoading: false,
        },
      };
    default:
      return state;
  }
};

interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  login: (phone: string) => Promise<void>;
  logout: () => Promise<void>;
  addPatient: (patient: Omit<Patient, 'id'>) => void;
  selectPatient: (patientId: string) => void;
  addAppointment: (appointment: Omit<Appointment, 'id'>) => void;
  getSelectedPatient: () => Patient | undefined;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      // Load user data from AsyncStorage
      const userData = await AsyncStorage.getItem('user');
      if (userData) {
        const user = JSON.parse(userData);
        dispatch({ type: 'SET_USER', payload: user });
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
      }

      // Load sample data
      dispatch({ type: 'SET_PATIENTS', payload: samplePatients });
      dispatch({ type: 'SET_APPOINTMENTS', payload: sampleAppointments });
      dispatch({ type: 'SET_MEDICAL_RECORDS', payload: sampleMedicalRecords });
      dispatch({ type: 'SET_DOCTORS', payload: sampleDoctors });
      dispatch({ type: 'SET_SPECIALTIES', payload: sampleSpecialties });
    } catch (error) {
      console.error('Error loading initial data:', error);
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const login = async (phone: string) => {
    try {
      // Simulate API call
      const user: User = {
        id: 'U001',
        phone,
        isVerified: true,
        patients: samplePatients,
        selectedPatientId: samplePatients[0]?.id,
      };

      await AsyncStorage.setItem('user', JSON.stringify(user));
      dispatch({ type: 'SET_USER', payload: user });
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await AsyncStorage.removeItem('user');
      dispatch({ type: 'LOGOUT' });
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const addPatient = (patientData: Omit<Patient, 'id'>) => {
    const newPatient: Patient = {
      ...patientData,
      id: `P${Date.now()}`,
    };
    dispatch({ type: 'ADD_PATIENT', payload: newPatient });
  };

  const selectPatient = (patientId: string) => {
    dispatch({ type: 'SELECT_PATIENT', payload: patientId });
  };

  const addAppointment = (appointmentData: Omit<Appointment, 'id'>) => {
    const newAppointment: Appointment = {
      ...appointmentData,
      id: `A${Date.now()}`,
    };
    dispatch({ type: 'ADD_APPOINTMENT', payload: newAppointment });
  };

  const getSelectedPatient = (): Patient | undefined => {
    if (!state.auth.user?.selectedPatientId) return undefined;
    return state.patients.find(p => p.id === state.auth.user?.selectedPatientId);
  };

  const value: AppContextType = {
    state,
    dispatch,
    login,
    logout,
    addPatient,
    selectPatient,
    addAppointment,
    getSelectedPatient,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
