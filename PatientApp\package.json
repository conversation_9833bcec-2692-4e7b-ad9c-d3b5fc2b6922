{"name": "patientapp", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "expo": "~53.0.10", "expo-linear-gradient": "^14.1.5", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "^2.25.0", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "private": true}