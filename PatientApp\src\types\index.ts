export interface Patient {
  id: string;
  name: string;
  age: number;
  gender: 'male' | 'female' | 'other';
  phone: string;
  email?: string;
  address?: string;
  relationship: 'self' | 'spouse' | 'child' | 'parent' | 'other';
  dateOfBirth: string;
}

export interface Doctor {
  id: string;
  name: string;
  specialty: string;
  experience: number;
  rating: number;
  reviews: number;
  avatar?: string;
  consultationFee: number;
  availableSlots: TimeSlot[];
}

export interface TimeSlot {
  id: string;
  time: string;
  date: string;
  available: boolean;
}

export interface Appointment {
  id: string;
  patientId: string;
  doctorId: string;
  date: string;
  time: string;
  status: 'upcoming' | 'completed' | 'cancelled';
  type: 'consultation' | 'follow-up' | 'emergency';
  notes?: string;
  amount: number;
  paymentStatus: 'paid' | 'pending' | 'failed';
}

export interface MedicalRecord {
  id: string;
  patientId: string;
  type: 'prescription' | 'lab-report' | 'scan' | 'diagnosis';
  title: string;
  description: string;
  date: string;
  doctorName: string;
  attachments?: string[];
}

export interface Specialty {
  id: string;
  name: string;
  description: string;
  icon: string;
  doctorCount: number;
}

export interface User {
  id: string;
  phone: string;
  isVerified: boolean;
  patients: Patient[];
  selectedPatientId?: string;
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface AppState {
  auth: AuthState;
  patients: Patient[];
  appointments: Appointment[];
  medicalRecords: MedicalRecord[];
  doctors: Doctor[];
  specialties: Specialty[];
}
