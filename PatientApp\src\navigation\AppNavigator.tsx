import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../context/AppContext';

// Auth Screens
import LoginScreen from '../screens/auth/LoginScreen';
import OTPVerificationScreen from '../screens/auth/OTPVerificationScreen';
import PatientSelectionScreen from '../screens/auth/PatientSelectionScreen';
import RegisterPatientScreen from '../screens/auth/RegisterPatientScreen';

// Main Screens
import HomeScreen from '../screens/main/HomeScreen';
import AppointmentsScreen from '../screens/main/AppointmentsScreen';
import MedicalRecordsScreen from '../screens/main/MedicalRecordsScreen';
import SettingsScreen from '../screens/main/SettingsScreen';

// Appointment Booking Screens
import SpecialtySelectionScreen from '../screens/booking/SpecialtySelectionScreen';
import DoctorSelectionScreen from '../screens/booking/DoctorSelectionScreen';
import DateTimeSelectionScreen from '../screens/booking/DateTimeSelectionScreen';
import PaymentScreen from '../screens/booking/PaymentScreen';
import ConfirmationScreen from '../screens/booking/ConfirmationScreen';

export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Login: undefined;
  OTPVerification: { phone: string };
  PatientSelection: undefined;
  RegisterPatient: undefined;
  SpecialtySelection: undefined;
  DoctorSelection: { specialtyId: string };
  DateTimeSelection: { doctorId: string };
  Payment: { appointmentData: any };
  Confirmation: { appointmentId: string };
};

export type MainTabParamList = {
  Home: undefined;
  Appointments: undefined;
  Records: undefined;
  Settings: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const AuthStack = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="Login" component={LoginScreen} />
    <Stack.Screen name="OTPVerification" component={OTPVerificationScreen} />
    <Stack.Screen name="PatientSelection" component={PatientSelectionScreen} />
    <Stack.Screen name="RegisterPatient" component={RegisterPatientScreen} />
  </Stack.Navigator>
);

const MainTabs = () => (
  <Tab.Navigator
    screenOptions={({ route }) => ({
      headerShown: false,
      tabBarIcon: ({ focused, color, size }) => {
        let iconName: keyof typeof Ionicons.glyphMap;

        if (route.name === 'Home') {
          iconName = focused ? 'home' : 'home-outline';
        } else if (route.name === 'Appointments') {
          iconName = focused ? 'calendar' : 'calendar-outline';
        } else if (route.name === 'Records') {
          iconName = focused ? 'document-text' : 'document-text-outline';
        } else if (route.name === 'Settings') {
          iconName = focused ? 'settings' : 'settings-outline';
        } else {
          iconName = 'help-outline';
        }

        return <Ionicons name={iconName} size={size} color={color} />;
      },
      tabBarActiveTintColor: '#1e40af',
      tabBarInactiveTintColor: '#64748b',
      tabBarStyle: {
        backgroundColor: 'white',
        borderTopWidth: 1,
        borderTopColor: '#e2e8f0',
        paddingBottom: 5,
        paddingTop: 5,
        height: 60,
      },
      tabBarLabelStyle: {
        fontSize: 12,
        fontWeight: '500',
      },
    })}
  >
    <Tab.Screen name="Home" component={HomeScreen} />
    <Tab.Screen name="Appointments" component={AppointmentsScreen} />
    <Tab.Screen name="Records" component={MedicalRecordsScreen} />
    <Tab.Screen name="Settings" component={SettingsScreen} />
  </Tab.Navigator>
);

const AppNavigator = () => {
  const { state } = useApp();

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!state.auth.isAuthenticated ? (
          <Stack.Screen name="Auth" component={AuthStack} />
        ) : (
          <>
            <Stack.Screen name="Main" component={MainTabs} />
            <Stack.Screen name="SpecialtySelection" component={SpecialtySelectionScreen} />
            <Stack.Screen name="DoctorSelection" component={DoctorSelectionScreen} />
            <Stack.Screen name="DateTimeSelection" component={DateTimeSelectionScreen} />
            <Stack.Screen name="Payment" component={PaymentScreen} />
            <Stack.Screen name="Confirmation" component={ConfirmationScreen} />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
