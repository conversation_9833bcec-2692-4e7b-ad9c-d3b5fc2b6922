import React, { useState } from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { useApp } from '../../context/AppContext';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';

type DateTimeSelectionScreenNavigationProp = StackNavigationProp<RootStackParamList, 'DateTimeSelection'>;
type DateTimeSelectionScreenRouteProp = RouteProp<RootStackParamList, 'DateTimeSelection'>;

interface Props {
  navigation: DateTimeSelectionScreenNavigationProp;
  route: DateTimeSelectionScreenRouteProp;
}

const DateTimeSelectionScreen: React.FC<Props> = ({ navigation, route }) => {
  const { doctorId } = route.params;
  const { state, getSelectedPatient } = useApp();
  const [selectedDate, setSelectedDate] = useState('2024-06-15');
  const [selectedTime, setSelectedTime] = useState('09:00 AM');
  
  const doctor = state.doctors.find(d => d.id === doctorId);
  const selectedPatient = getSelectedPatient();

  const handleContinue = () => {
    const appointmentData = {
      patientId: selectedPatient?.id,
      doctorId,
      date: selectedDate,
      time: selectedTime,
      status: 'upcoming' as const,
      type: 'consultation' as const,
      amount: (doctor?.consultationFee || 0) + 50 + Math.round(((doctor?.consultationFee || 0) + 50) * 0.18),
      paymentStatus: 'pending' as const,
    };
    
    navigation.navigate('Payment', { appointmentData });
  };

  const timeSlots = [
    '09:00 AM', '09:30 AM', '10:00 AM', '10:30 AM',
    '11:00 AM', '11:30 AM', '04:00 PM', '04:30 PM', '05:00 PM'
  ];

  return (
    <SafeAreaView className="flex-1 bg-secondary-50">
      <View className="flex-1">
        {/* Header */}
        <View className="bg-white px-6 py-4 shadow-sm">
          <View className="flex-row items-center">
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              className="mr-4"
            >
              <Ionicons name="arrow-back" size={24} color="#374151" />
            </TouchableOpacity>
            <View className="flex-1">
              <Text className="text-xl font-bold text-secondary-800">Select Date & Time</Text>
              <Text className="text-secondary-500">{doctor?.name}</Text>
            </View>
          </View>
        </View>

        <ScrollView className="flex-1 px-6 py-6">
          {/* Calendar */}
          <Card variant="elevated" className="mb-6">
            <Text className="text-lg font-semibold text-secondary-800 mb-4">Select Date</Text>
            <View className="items-center">
              <Text className="text-base font-medium text-secondary-600 mb-4">June 2024</Text>
              <View className="bg-primary-500 rounded-full h-16 w-16 items-center justify-center">
                <Text className="text-white font-bold text-xl">15</Text>
              </View>
              <Text className="text-secondary-600 mt-2">Thursday, June 15</Text>
            </View>
          </Card>

          {/* Time Slots */}
          <Card variant="elevated" className="mb-6">
            <Text className="text-lg font-semibold text-secondary-800 mb-4">Available Time Slots</Text>
            <View className="flex-row flex-wrap">
              {timeSlots.map((time) => (
                <TouchableOpacity
                  key={time}
                  onPress={() => setSelectedTime(time)}
                  className={`
                    px-4 py-3 rounded-lg mr-3 mb-3 border
                    ${selectedTime === time 
                      ? 'bg-primary-500 border-primary-500' 
                      : 'bg-secondary-50 border-secondary-200'
                    }
                  `}
                >
                  <Text className={`
                    font-medium
                    ${selectedTime === time ? 'text-white' : 'text-secondary-600'}
                  `}>
                    {time}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </Card>

          {/* Appointment Summary */}
          <Card variant="filled" className="bg-blue-50 border-blue-200 mb-6">
            <Text className="text-lg font-semibold text-secondary-800 mb-3">Appointment Summary</Text>
            
            <View className="space-y-2">
              <View className="flex-row items-center">
                <Ionicons name="person-outline" size={16} color="#3b82f6" />
                <Text className="text-secondary-600 ml-2">{doctor?.name}</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="calendar-outline" size={16} color="#3b82f6" />
                <Text className="text-secondary-600 ml-2">Thursday, June 15, 2024</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="time-outline" size={16} color="#3b82f6" />
                <Text className="text-secondary-600 ml-2">{selectedTime}</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="card-outline" size={16} color="#3b82f6" />
                <Text className="text-secondary-600 ml-2">₹{doctor?.consultationFee} + taxes</Text>
              </View>
            </View>
          </Card>

          <Button
            title="Continue to Payment"
            onPress={handleContinue}
            fullWidth
            size="large"
          />
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default DateTimeSelectionScreen;
