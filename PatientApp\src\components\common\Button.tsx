import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator, ViewStyle, TextStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  fullWidth = false,
  style,
  textStyle,
}) => {
  const getButtonClasses = () => {
    const baseClasses = 'rounded-xl flex-row items-center justify-center';
    const sizeClasses = {
      small: 'px-4 py-2',
      medium: 'px-6 py-3',
      large: 'px-8 py-4',
    };
    const widthClass = fullWidth ? 'w-full' : '';
    
    return `${baseClasses} ${sizeClasses[size]} ${widthClass}`;
  };

  const getTextClasses = () => {
    const baseClasses = 'font-semibold text-center';
    const sizeClasses = {
      small: 'text-sm',
      medium: 'text-base',
      large: 'text-lg',
    };
    const variantClasses = {
      primary: 'text-white',
      secondary: 'text-secondary-700',
      outline: 'text-primary-600',
      ghost: 'text-primary-600',
    };
    
    return `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]}`;
  };

  const renderContent = () => (
    <>
      {loading && (
        <ActivityIndicator 
          size="small" 
          color={variant === 'primary' ? 'white' : '#1e40af'} 
          style={{ marginRight: icon || title ? 8 : 0 }}
        />
      )}
      {!loading && icon && (
        <div style={{ marginRight: title ? 8 : 0 }}>
          {icon}
        </div>
      )}
      {title && (
        <Text className={getTextClasses()} style={textStyle}>
          {title}
        </Text>
      )}
    </>
  );

  if (variant === 'primary') {
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled || loading}
        style={[
          {
            opacity: disabled || loading ? 0.6 : 1,
          },
          style,
        ]}
        className={getButtonClasses()}
      >
        <LinearGradient
          colors={['#3b82f6', '#1e40af']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          className="rounded-xl flex-row items-center justify-center px-6 py-3 w-full"
        >
          {renderContent()}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  const variantStyles = {
    secondary: 'bg-secondary-100 border border-secondary-200',
    outline: 'bg-transparent border-2 border-primary-600',
    ghost: 'bg-transparent',
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      className={`${getButtonClasses()} ${variantStyles[variant]}`}
      style={[
        {
          opacity: disabled || loading ? 0.6 : 1,
        },
        style,
      ]}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

export default Button;
