import React, { useState } from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { useApp } from '../../context/AppContext';
import { Patient } from '../../types';
import Input from '../../components/common/Input';
import Button from '../../components/common/Button';

type RegisterPatientScreenNavigationProp = StackNavigationProp<RootStackParamList, 'RegisterPatient'>;

interface Props {
  navigation: RegisterPatientScreenNavigationProp;
}

const RegisterPatientScreen: React.FC<Props> = ({ navigation }) => {
  const { addPatient, selectPatient } = useApp();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    dateOfBirth: '',
    gender: '',
    phone: '',
    email: '',
    address: '',
    relationship: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Full name is required';
    }

    if (!formData.dateOfBirth) {
      newErrors.dateOfBirth = 'Date of birth is required';
    }

    if (!formData.gender) {
      newErrors.gender = 'Gender is required';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Mobile number is required';
    } else if (!/^[6-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid 10-digit mobile number';
    }

    if (!formData.relationship) {
      newErrors.relationship = 'Relationship is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  const handleRegister = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const age = calculateAge(formData.dateOfBirth);
      
      const newPatient: Omit<Patient, 'id'> = {
        name: formData.name.trim(),
        age,
        gender: formData.gender as 'male' | 'female' | 'other',
        phone: `+91${formData.phone}`,
        email: formData.email.trim() || undefined,
        address: formData.address.trim() || undefined,
        relationship: formData.relationship as Patient['relationship'],
        dateOfBirth: formData.dateOfBirth,
      };

      addPatient(newPatient);
      
      Alert.alert(
        'Success',
        'Patient registered successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to register patient. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <View className="flex-1">
        {/* Header */}
        <View className="flex-row items-center px-6 py-4 bg-white shadow-sm">
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="mr-4"
          >
            <Ionicons name="arrow-back" size={24} color="#374151" />
          </TouchableOpacity>
          <Text className="text-xl font-bold text-secondary-800">Register New Patient</Text>
        </View>

        <ScrollView className="flex-1 px-6 py-6">
          <Input
            label="Full Name"
            value={formData.name}
            onChangeText={(value) => updateFormData('name', value)}
            placeholder="Enter full name"
            error={errors.name}
            required
            leftIcon="person-outline"
          />

          <View className="flex-row space-x-4 mb-4">
            <View className="flex-1">
              <Input
                label="Date of Birth"
                value={formData.dateOfBirth}
                onChangeText={(value) => updateFormData('dateOfBirth', value)}
                placeholder="YYYY-MM-DD"
                error={errors.dateOfBirth}
                required
                leftIcon="calendar-outline"
              />
            </View>
            
            <View className="flex-1">
              <Text className="text-sm font-medium text-secondary-700 mb-2">
                Gender <Text className="text-error-500">*</Text>
              </Text>
              <View className="flex-row space-x-2">
                {['male', 'female', 'other'].map((gender) => (
                  <TouchableOpacity
                    key={gender}
                    onPress={() => updateFormData('gender', gender)}
                    className={`
                      flex-1 py-3 px-4 rounded-xl border-2 items-center
                      ${formData.gender === gender 
                        ? 'border-primary-500 bg-primary-50' 
                        : 'border-secondary-200 bg-white'
                      }
                    `}
                  >
                    <Text className={`
                      text-sm font-medium capitalize
                      ${formData.gender === gender ? 'text-primary-600' : 'text-secondary-600'}
                    `}>
                      {gender}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              {errors.gender && (
                <Text className="text-sm text-error-500 mt-1 ml-1">{errors.gender}</Text>
              )}
            </View>
          </View>

          <View className="mb-4">
            <Text className="text-sm font-medium text-secondary-700 mb-2">
              Mobile Number <Text className="text-error-500">*</Text>
            </Text>
            <View className="flex-row">
              <View className="bg-secondary-100 border border-secondary-200 rounded-l-xl px-4 py-3 border-r-0">
                <Text className="text-secondary-600 font-medium">+91</Text>
              </View>
              <Input
                value={formData.phone}
                onChangeText={(value) => updateFormData('phone', value)}
                placeholder="Enter mobile number"
                keyboardType="numeric"
                maxLength={10}
                error={errors.phone}
                containerStyle={{ marginBottom: 0, flex: 1 }}
                inputStyle={{ borderTopLeftRadius: 0, borderBottomLeftRadius: 0 }}
              />
            </View>
          </View>

          <Input
            label="Email (Optional)"
            value={formData.email}
            onChangeText={(value) => updateFormData('email', value)}
            placeholder="Enter email address"
            keyboardType="email-address"
            leftIcon="mail-outline"
          />

          <Input
            label="Address"
            value={formData.address}
            onChangeText={(value) => updateFormData('address', value)}
            placeholder="Enter complete address"
            multiline
            numberOfLines={3}
            leftIcon="location-outline"
          />

          <View className="mb-6">
            <Text className="text-sm font-medium text-secondary-700 mb-2">
              Relationship with you <Text className="text-error-500">*</Text>
            </Text>
            <View className="flex-row flex-wrap">
              {['self', 'spouse', 'child', 'parent', 'other'].map((relationship) => (
                <TouchableOpacity
                  key={relationship}
                  onPress={() => updateFormData('relationship', relationship)}
                  className={`
                    py-2 px-4 rounded-full border mr-2 mb-2
                    ${formData.relationship === relationship 
                      ? 'border-primary-500 bg-primary-50' 
                      : 'border-secondary-200 bg-white'
                    }
                  `}
                >
                  <Text className={`
                    text-sm font-medium capitalize
                    ${formData.relationship === relationship ? 'text-primary-600' : 'text-secondary-600'}
                  `}>
                    {relationship}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            {errors.relationship && (
              <Text className="text-sm text-error-500 mt-1 ml-1">{errors.relationship}</Text>
            )}
          </View>

          <Button
            title="Register Patient"
            onPress={handleRegister}
            loading={loading}
            fullWidth
            size="large"
          />
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default RegisterPatientScreen;
