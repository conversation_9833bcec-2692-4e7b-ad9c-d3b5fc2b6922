import React, { useState } from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import Card from '../../components/common/Card';

const MedicalRecordsScreen: React.FC = () => {
  const { state, getSelectedPatient } = useApp();
  const [activeFilter, setActiveFilter] = useState<'all' | 'prescription' | 'lab-report' | 'scan' | 'diagnosis'>('all');
  const selectedPatient = getSelectedPatient();

  const filteredRecords = state.medicalRecords.filter(record => {
    const matchesPatient = record.patientId === selectedPatient?.id;
    const matchesFilter = activeFilter === 'all' || record.type === activeFilter;
    return matchesPatient && matchesFilter;
  });

  const getRecordIcon = (type: string) => {
    switch (type) {
      case 'prescription':
        return 'medical';
      case 'lab-report':
        return 'flask';
      case 'scan':
        return 'scan';
      case 'diagnosis':
        return 'clipboard';
      default:
        return 'document-text';
    }
  };

  const getRecordColor = (type: string) => {
    switch (type) {
      case 'prescription':
        return '#ef4444';
      case 'lab-report':
        return '#f59e0b';
      case 'scan':
        return '#8b5cf6';
      case 'diagnosis':
        return '#10b981';
      default:
        return '#3b82f6';
    }
  };

  const filters = [
    { key: 'all', label: 'All', count: state.medicalRecords.filter(r => r.patientId === selectedPatient?.id).length },
    { key: 'prescription', label: 'Prescriptions', count: state.medicalRecords.filter(r => r.patientId === selectedPatient?.id && r.type === 'prescription').length },
    { key: 'lab-report', label: 'Lab Reports', count: state.medicalRecords.filter(r => r.patientId === selectedPatient?.id && r.type === 'lab-report').length },
    { key: 'scan', label: 'Scans', count: state.medicalRecords.filter(r => r.patientId === selectedPatient?.id && r.type === 'scan').length },
    { key: 'diagnosis', label: 'Diagnosis', count: state.medicalRecords.filter(r => r.patientId === selectedPatient?.id && r.type === 'diagnosis').length },
  ];

  return (
    <SafeAreaView className="flex-1 bg-secondary-50">
      <View className="flex-1">
        {/* Header */}
        <View className="bg-white px-6 py-4 shadow-sm">
          <Text className="text-2xl font-bold text-secondary-800">Medical Records</Text>
          <Text className="text-secondary-500 mt-1">Your health information in one place</Text>
        </View>

        {/* Filter Tabs */}
        <View className="bg-white px-6 py-4 border-b border-secondary-100">
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View className="flex-row space-x-3">
              {filters.map((filter) => (
                <TouchableOpacity
                  key={filter.key}
                  onPress={() => setActiveFilter(filter.key as any)}
                  className={`
                    px-4 py-2 rounded-full border
                    ${activeFilter === filter.key 
                      ? 'bg-primary-500 border-primary-500' 
                      : 'bg-white border-secondary-200'
                    }
                  `}
                >
                  <View className="flex-row items-center">
                    <Text className={`
                      font-medium
                      ${activeFilter === filter.key ? 'text-white' : 'text-secondary-600'}
                    `}>
                      {filter.label}
                    </Text>
                    {filter.count > 0 && (
                      <View className={`
                        ml-2 h-5 w-5 rounded-full items-center justify-center
                        ${activeFilter === filter.key ? 'bg-white/20' : 'bg-secondary-100'}
                      `}>
                        <Text className={`
                          text-xs font-bold
                          ${activeFilter === filter.key ? 'text-white' : 'text-secondary-600'}
                        `}>
                          {filter.count}
                        </Text>
                      </View>
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        <ScrollView className="flex-1 px-6 py-6">
          {filteredRecords.length > 0 ? (
            filteredRecords.map((record) => (
              <Card key={record.id} variant="elevated" className="mb-4">
                <View className="flex-row items-start">
                  <View 
                    className="h-14 w-14 rounded-full items-center justify-center mr-4"
                    style={{ backgroundColor: `${getRecordColor(record.type)}15` }}
                  >
                    <Ionicons 
                      name={getRecordIcon(record.type) as any} 
                      size={24} 
                      color={getRecordColor(record.type)} 
                    />
                  </View>
                  
                  <View className="flex-1">
                    <View className="flex-row items-start justify-between mb-2">
                      <View className="flex-1">
                        <Text className="text-lg font-semibold text-secondary-800">
                          {record.title}
                        </Text>
                        <Text className="text-secondary-500 mt-1">
                          {record.description}
                        </Text>
                      </View>
                      <View className="px-2 py-1 rounded-full bg-secondary-100">
                        <Text className="text-xs font-medium text-secondary-600 capitalize">
                          {record.type.replace('-', ' ')}
                        </Text>
                      </View>
                    </View>
                    
                    <View className="flex-row items-center mb-3">
                      <Ionicons name="person-outline" size={16} color="#64748b" />
                      <Text className="text-secondary-600 ml-2">{record.doctorName}</Text>
                      <Ionicons name="calendar-outline" size={16} color="#64748b" style={{ marginLeft: 16 }} />
                      <Text className="text-secondary-600 ml-2">{record.date}</Text>
                    </View>
                    
                    {record.attachments && record.attachments.length > 0 && (
                      <View className="flex-row items-center">
                        <Ionicons name="attach-outline" size={16} color="#64748b" />
                        <Text className="text-secondary-600 ml-2">
                          {record.attachments.length} attachment{record.attachments.length > 1 ? 's' : ''}
                        </Text>
                        <TouchableOpacity className="ml-auto">
                          <Text className="text-primary-600 font-medium">View</Text>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                </View>
              </Card>
            ))
          ) : (
            <Card variant="outlined" className="items-center py-12">
              <Ionicons name="document-text-outline" size={64} color="#cbd5e1" />
              <Text className="text-lg font-medium text-secondary-500 mt-4">
                No {activeFilter === 'all' ? 'medical records' : activeFilter.replace('-', ' ')} found
              </Text>
              <Text className="text-secondary-400 text-center mt-2">
                Your medical records will appear here after your appointments
              </Text>
            </Card>
          )}

          {/* Health Summary Card */}
          {filteredRecords.length > 0 && activeFilter === 'all' && (
            <Card variant="filled" className="mt-6 bg-gradient-to-r from-blue-50 to-purple-50">
              <View className="flex-row items-center justify-between mb-4">
                <Text className="text-lg font-bold text-secondary-800">Health Summary</Text>
                <Ionicons name="analytics-outline" size={24} color="#3b82f6" />
              </View>
              
              <View className="flex-row justify-between">
                <View className="items-center">
                  <Text className="text-2xl font-bold text-primary-600">
                    {state.medicalRecords.filter(r => r.patientId === selectedPatient?.id).length}
                  </Text>
                  <Text className="text-sm text-secondary-500">Total Records</Text>
                </View>
                <View className="items-center">
                  <Text className="text-2xl font-bold text-success-600">
                    {state.appointments.filter(a => a.patientId === selectedPatient?.id && a.status === 'completed').length}
                  </Text>
                  <Text className="text-sm text-secondary-500">Consultations</Text>
                </View>
                <View className="items-center">
                  <Text className="text-2xl font-bold text-warning-600">
                    {state.medicalRecords.filter(r => r.patientId === selectedPatient?.id && r.type === 'lab-report').length}
                  </Text>
                  <Text className="text-sm text-secondary-500">Lab Tests</Text>
                </View>
              </View>
            </Card>
          )}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default MedicalRecordsScreen;
