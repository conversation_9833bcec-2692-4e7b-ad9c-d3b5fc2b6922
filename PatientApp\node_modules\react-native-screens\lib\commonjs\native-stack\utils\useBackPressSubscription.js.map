{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "e", "__esModule", "default", "useBackPressSubscription", "onBackPress", "isDisabled", "isActive", "setIsActive", "React", "useState", "subscription", "useRef", "clearSubscription", "useCallback", "shouldSetActive", "current", "remove", "undefined", "createSubscription", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "handleAttached", "handleDetached", "useEffect"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/useBackPressSubscription.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAAoE,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAcpE;AACA;AACA;AACA;AACO,SAASG,wBAAwBA,CAAC;EACvCC,WAAW;EACXC;AACI,CAAC,EAA4B;EACjC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGC,cAAK,CAACC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMC,YAAY,GAAGF,cAAK,CAACG,MAAM,CAAsC,CAAC;EAExE,MAAMC,iBAAiB,GAAGJ,cAAK,CAACK,WAAW,CAAC,CAACC,eAAe,GAAG,IAAI,KAAK;IACtEJ,YAAY,CAACK,OAAO,EAAEC,MAAM,CAAC,CAAC;IAC9BN,YAAY,CAACK,OAAO,GAAGE,SAAS;IAChC,IAAIH,eAAe,EAAEP,WAAW,CAAC,KAAK,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,kBAAkB,GAAGV,cAAK,CAACK,WAAW,CAAC,MAAM;IACjD,IAAI,CAACR,UAAU,EAAE;MACfK,YAAY,CAACK,OAAO,EAAEC,MAAM,CAAC,CAAC;MAC9BN,YAAY,CAACK,OAAO,GAAGI,wBAAW,CAACC,gBAAgB,CACjD,mBAAmB,EACnBhB,WACF,CAAC;MACDG,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC,EAAE,CAACF,UAAU,EAAED,WAAW,CAAC,CAAC;EAE7B,MAAMiB,cAAc,GAAGb,cAAK,CAACK,WAAW,CAAC,MAAM;IAC7C,IAAIP,QAAQ,EAAE;MACZY,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACA,kBAAkB,EAAEZ,QAAQ,CAAC,CAAC;EAElC,MAAMgB,cAAc,GAAGd,cAAK,CAACK,WAAW,CAAC,MAAM;IAC7CD,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvBJ,cAAK,CAACe,SAAS,CAAC,MAAM;IACpB,IAAIlB,UAAU,EAAE;MACdO,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,UAAU,EAAEO,iBAAiB,CAAC,CAAC;EAEnC,OAAO;IACLS,cAAc;IACdC,cAAc;IACdJ,kBAAkB;IAClBN;EACF,CAAC;AACH", "ignoreList": []}