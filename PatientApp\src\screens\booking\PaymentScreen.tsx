import React, { useState } from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { useApp } from '../../context/AppContext';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';

type PaymentScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Payment'>;
type PaymentScreenRouteProp = RouteProp<RootStackParamList, 'Payment'>;

interface Props {
  navigation: PaymentScreenNavigationProp;
  route: PaymentScreenRouteProp;
}

const PaymentScreen: React.FC<Props> = ({ navigation, route }) => {
  const { appointmentData } = route.params;
  const { state, addAppointment } = useApp();
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('upi');
  const [loading, setLoading] = useState(false);
  
  const doctor = state.doctors.find(d => d.id === appointmentData.doctorId);
  const consultationFee = doctor?.consultationFee || 0;
  const platformFee = 50;
  const gst = Math.round((consultationFee + platformFee) * 0.18);
  const totalAmount = consultationFee + platformFee + gst;

  const paymentMethods = [
    {
      id: 'upi',
      name: 'UPI',
      icon: 'card-outline',
      color: '#10b981',
      description: 'Pay using UPI apps',
    },
    {
      id: 'card',
      name: 'Credit/Debit Card',
      icon: 'card',
      color: '#3b82f6',
      description: 'Visa, Mastercard, Rupay',
    },
    {
      id: 'netbanking',
      name: 'Net Banking',
      icon: 'business',
      color: '#8b5cf6',
      description: 'All major banks',
    },
  ];

  const handlePayment = async () => {
    setLoading(true);
    
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Add appointment
      const newAppointment = {
        ...appointmentData,
        amount: totalAmount,
        paymentStatus: 'paid' as const,
      };
      
      addAppointment(newAppointment);
      
      // Navigate to confirmation
      navigation.navigate('Confirmation', { appointmentId: 'temp-id' });
    } catch (error) {
      console.error('Payment error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-secondary-50">
      <View className="flex-1">
        {/* Header */}
        <View className="bg-white px-6 py-4 shadow-sm">
          <View className="flex-row items-center">
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              className="mr-4"
            >
              <Ionicons name="arrow-back" size={24} color="#374151" />
            </TouchableOpacity>
            <View className="flex-1">
              <Text className="text-xl font-bold text-secondary-800">Payment</Text>
              <Text className="text-secondary-500">Complete your booking</Text>
            </View>
          </View>
        </View>

        <ScrollView className="flex-1 px-6 py-6">
          {/* Appointment Details */}
          <Card variant="filled" className="bg-blue-50 border-blue-200 mb-6">
            <Text className="text-lg font-semibold text-secondary-800 mb-3">Appointment Details</Text>
            
            <View className="space-y-2">
              <View className="flex-row items-center">
                <Ionicons name="person-outline" size={16} color="#3b82f6" />
                <Text className="text-secondary-600 ml-2">{doctor?.name} ({doctor?.specialty})</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="calendar-outline" size={16} color="#3b82f6" />
                <Text className="text-secondary-600 ml-2">{appointmentData.date}</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="time-outline" size={16} color="#3b82f6" />
                <Text className="text-secondary-600 ml-2">{appointmentData.time}</Text>
              </View>
            </View>
          </Card>

          {/* Payment Summary */}
          <Card variant="elevated" className="mb-6">
            <Text className="text-lg font-semibold text-secondary-800 mb-4">Payment Summary</Text>
            
            <View className="space-y-3">
              <View className="flex-row justify-between">
                <Text className="text-secondary-600">Consultation Fee</Text>
                <Text className="text-secondary-800 font-medium">₹{consultationFee}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-secondary-600">Platform Fee</Text>
                <Text className="text-secondary-800 font-medium">₹{platformFee}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-secondary-600">GST (18%)</Text>
                <Text className="text-secondary-800 font-medium">₹{gst}</Text>
              </View>
              <View className="border-t border-secondary-200 pt-3">
                <View className="flex-row justify-between">
                  <Text className="text-lg font-semibold text-secondary-800">Total Amount</Text>
                  <Text className="text-lg font-bold text-primary-600">₹{totalAmount}</Text>
                </View>
              </View>
            </View>
          </Card>

          {/* Payment Methods */}
          <Card variant="elevated" className="mb-6">
            <Text className="text-lg font-semibold text-secondary-800 mb-4">Payment Method</Text>
            
            {paymentMethods.map((method, index) => (
              <TouchableOpacity
                key={method.id}
                onPress={() => setSelectedPaymentMethod(method.id)}
                className={`
                  flex-row items-center p-4 rounded-xl border mb-3
                  ${selectedPaymentMethod === method.id 
                    ? 'border-primary-500 bg-primary-50' 
                    : 'border-secondary-200 bg-white'
                  }
                `}
              >
                <View className="h-10 w-10 rounded-lg items-center justify-center mr-4"
                      style={{ backgroundColor: `${method.color}15` }}>
                  <Ionicons name={method.icon as any} size={20} color={method.color} />
                </View>
                
                <View className="flex-1">
                  <Text className="font-medium text-secondary-800">{method.name}</Text>
                  <Text className="text-sm text-secondary-500">{method.description}</Text>
                </View>
                
                <View className={`
                  h-5 w-5 rounded-full border-2
                  ${selectedPaymentMethod === method.id 
                    ? 'border-primary-500 bg-primary-500' 
                    : 'border-secondary-300'
                  }
                `}>
                  {selectedPaymentMethod === method.id && (
                    <View className="h-full w-full rounded-full bg-white scale-50" />
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </Card>

          <Button
            title={`Pay ₹${totalAmount}`}
            onPress={handlePayment}
            loading={loading}
            fullWidth
            size="large"
          />

          {/* Security Note */}
          <View className="mt-4 p-4 bg-green-50 rounded-xl">
            <View className="flex-row items-start">
              <Ionicons name="shield-checkmark" size={20} color="#10b981" style={{ marginTop: 2, marginRight: 8 }} />
              <View className="flex-1">
                <Text className="text-sm font-medium text-green-800 mb-1">
                  Secure Payment
                </Text>
                <Text className="text-sm text-green-600">
                  Your payment information is encrypted and secure. We don't store your card details.
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default PaymentScreen;
