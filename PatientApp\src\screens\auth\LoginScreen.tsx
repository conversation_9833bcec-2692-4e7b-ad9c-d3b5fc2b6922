import React, { useState } from 'react';
import { View, Text, SafeAreaView, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../navigation/AppNavigator';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';

type LoginScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Login'>;

interface Props {
  navigation: LoginScreenNavigationProp;
}

const LoginScreen: React.FC<Props> = ({ navigation }) => {
  const [phone, setPhone] = useState('');
  const [loading, setLoading] = useState(false);

  const validatePhone = (phoneNumber: string) => {
    const phoneRegex = /^[6-9]\d{9}$/;
    return phoneRegex.test(phoneNumber);
  };

  const handleSendOTP = async () => {
    if (!phone.trim()) {
      Alert.alert('Error', 'Please enter your mobile number');
      return;
    }

    if (!validatePhone(phone)) {
      Alert.alert('Error', 'Please enter a valid 10-digit mobile number');
      return;
    }

    setLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      navigation.navigate('OTPVerification', { phone: `+91${phone}` });
    } catch (error) {
      Alert.alert('Error', 'Failed to send OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <LinearGradient
        colors={['#3b82f6', '#1e40af']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        className="flex-1"
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          className="flex-1 justify-center px-6"
        >
          <View className="items-center mb-12">
            {/* App Logo */}
            <View className="h-20 w-20 rounded-full bg-white/20 items-center justify-center mb-6">
              <Ionicons name="medical" size={40} color="white" />
            </View>
            
            <Text className="text-3xl font-bold text-white mb-2">MediCare</Text>
            <Text className="text-lg text-blue-100">Patient Portal</Text>
          </View>

          <View className="bg-white rounded-2xl p-6 shadow-xl">
            <Text className="text-2xl font-bold text-secondary-800 mb-2 text-center">
              Welcome Back
            </Text>
            <Text className="text-secondary-500 mb-8 text-center">
              Enter your mobile number to continue
            </Text>

            <View className="mb-6">
              <Text className="text-sm font-medium text-secondary-700 mb-2">
                Mobile Number
              </Text>
              <View className="flex-row">
                <View className="bg-secondary-100 border border-secondary-200 rounded-l-xl px-4 py-3 border-r-0">
                  <Text className="text-secondary-600 font-medium">+91</Text>
                </View>
                <Input
                  value={phone}
                  onChangeText={setPhone}
                  placeholder="Enter your mobile number"
                  keyboardType="numeric"
                  maxLength={10}
                  containerStyle={{ marginBottom: 0, flex: 1 }}
                  inputStyle={{ borderTopLeftRadius: 0, borderBottomLeftRadius: 0 }}
                />
              </View>
            </View>

            <Button
              title="Send OTP"
              onPress={handleSendOTP}
              loading={loading}
              fullWidth
              size="large"
            />

            <View className="mt-6 pt-6 border-t border-secondary-100">
              <Text className="text-center text-sm text-secondary-500">
                By continuing, you agree to our{' '}
                <Text className="text-primary-600 font-medium">Terms of Service</Text>
                {' '}and{' '}
                <Text className="text-primary-600 font-medium">Privacy Policy</Text>
              </Text>
            </View>
          </View>

          <View className="mt-8 items-center">
            <Text className="text-blue-100 text-sm">
              New to MediCare?{' '}
              <Text className="text-white font-semibold">
                Register after login
              </Text>
            </Text>
          </View>
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  );
};

export default LoginScreen;
