import React, { useState } from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { useApp } from '../../context/AppContext';
import Card from '../../components/common/Card';

type SpecialtySelectionScreenNavigationProp = StackNavigationProp<RootStackParamList, 'SpecialtySelection'>;

interface Props {
  navigation: SpecialtySelectionScreenNavigationProp;
}

const SpecialtySelectionScreen: React.FC<Props> = ({ navigation }) => {
  const { state } = useApp();
  const [searchQuery, setSearchQuery] = useState('');

  const filteredSpecialties = state.specialties.filter(specialty =>
    specialty.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    specialty.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getSpecialtyIcon = (name: string) => {
    const iconMap: Record<string, string> = {
      'Cardiology': 'heart',
      'Dermatology': 'person',
      'Orthopedics': 'body',
      'Neurology': 'brain',
      'Pediatrics': 'happy',
      'Gynecology': 'female',
      'Ophthalmology': 'eye',
      'ENT': 'ear',
      'Psychiatry': 'head',
      'General Medicine': 'medical',
    };
    return iconMap[name] || 'medical';
  };

  const getSpecialtyColor = (index: number) => {
    const colors = [
      '#ef4444', '#f59e0b', '#10b981', '#3b82f6', 
      '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16'
    ];
    return colors[index % colors.length];
  };

  const handleSpecialtySelect = (specialtyId: string) => {
    navigation.navigate('DoctorSelection', { specialtyId });
  };

  return (
    <SafeAreaView className="flex-1 bg-secondary-50">
      <View className="flex-1">
        {/* Header */}
        <View className="bg-white px-6 py-4 shadow-sm">
          <View className="flex-row items-center">
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              className="mr-4"
            >
              <Ionicons name="arrow-back" size={24} color="#374151" />
            </TouchableOpacity>
            <View className="flex-1">
              <Text className="text-xl font-bold text-secondary-800">Book Appointment</Text>
              <Text className="text-secondary-500">Select medical specialty</Text>
            </View>
          </View>
        </View>

        {/* Search Bar */}
        <View className="bg-white px-6 py-4 border-b border-secondary-100">
          <View className="flex-row items-center bg-secondary-50 rounded-xl px-4 py-3">
            <Ionicons name="search" size={20} color="#64748b" />
            <TextInput
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search specialties..."
              className="flex-1 ml-3 text-base text-secondary-800"
              placeholderTextColor="#94a3b8"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color="#94a3b8" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        <ScrollView className="flex-1 px-6 py-6">
          {/* Popular Specialties */}
          {searchQuery === '' && (
            <View className="mb-6">
              <Text className="text-lg font-bold text-secondary-800 mb-4">Popular Specialties</Text>
              <View className="flex-row flex-wrap justify-between">
                {state.specialties.slice(0, 4).map((specialty, index) => (
                  <TouchableOpacity
                    key={specialty.id}
                    onPress={() => handleSpecialtySelect(specialty.id)}
                    className="w-[48%] mb-4"
                  >
                    <Card variant="elevated" className="items-center py-6">
                      <View 
                        className="h-16 w-16 rounded-full items-center justify-center mb-3"
                        style={{ backgroundColor: `${getSpecialtyColor(index)}15` }}
                      >
                        <Ionicons 
                          name={getSpecialtyIcon(specialty.name) as any} 
                          size={28} 
                          color={getSpecialtyColor(index)} 
                        />
                      </View>
                      <Text className="font-semibold text-secondary-800 text-center mb-1">
                        {specialty.name}
                      </Text>
                      <Text className="text-xs text-secondary-500 text-center mb-2">
                        {specialty.description}
                      </Text>
                      <Text className="text-xs text-primary-600 font-medium">
                        {specialty.doctorCount} doctors
                      </Text>
                    </Card>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}

          {/* All Specialties */}
          <View>
            <Text className="text-lg font-bold text-secondary-800 mb-4">
              {searchQuery ? `Search Results (${filteredSpecialties.length})` : 'All Specialties'}
            </Text>
            
            {filteredSpecialties.length > 0 ? (
              filteredSpecialties.map((specialty, index) => (
                <Card
                  key={specialty.id}
                  onPress={() => handleSpecialtySelect(specialty.id)}
                  variant="elevated"
                  className="mb-3"
                >
                  <View className="flex-row items-center">
                    <View 
                      className="h-14 w-14 rounded-full items-center justify-center mr-4"
                      style={{ backgroundColor: `${getSpecialtyColor(index)}15` }}
                    >
                      <Ionicons 
                        name={getSpecialtyIcon(specialty.name) as any} 
                        size={24} 
                        color={getSpecialtyColor(index)} 
                      />
                    </View>
                    
                    <View className="flex-1">
                      <Text className="text-lg font-semibold text-secondary-800">
                        {specialty.name}
                      </Text>
                      <Text className="text-secondary-500 mt-1">
                        {specialty.description}
                      </Text>
                      <Text className="text-sm text-primary-600 mt-1">
                        {specialty.doctorCount} doctors available
                      </Text>
                    </View>
                    
                    <Ionicons name="chevron-forward" size={20} color="#94a3b8" />
                  </View>
                </Card>
              ))
            ) : (
              <Card variant="outlined" className="items-center py-12">
                <Ionicons name="search-outline" size={64} color="#cbd5e1" />
                <Text className="text-lg font-medium text-secondary-500 mt-4">
                  No specialties found
                </Text>
                <Text className="text-secondary-400 text-center mt-2">
                  Try searching with different keywords
                </Text>
              </Card>
            )}
          </View>

          {/* Emergency Banner */}
          <Card variant="filled" className="mt-6 bg-red-50 border-red-200">
            <View className="flex-row items-center">
              <View className="h-12 w-12 rounded-full bg-red-100 items-center justify-center mr-4">
                <Ionicons name="warning" size={24} color="#ef4444" />
              </View>
              <View className="flex-1">
                <Text className="font-semibold text-red-800">Medical Emergency?</Text>
                <Text className="text-sm text-red-600 mt-1">
                  For urgent medical care, call emergency services
                </Text>
              </View>
              <TouchableOpacity className="bg-red-500 px-4 py-2 rounded-lg">
                <Text className="text-white font-medium">Call 108</Text>
              </TouchableOpacity>
            </View>
          </Card>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default SpecialtySelectionScreen;
