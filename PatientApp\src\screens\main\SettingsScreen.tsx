import React from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../context/AppContext';
import Card from '../../components/common/Card';

const SettingsScreen: React.FC = () => {
  const { state, logout, getSelectedPatient } = useApp();
  const selectedPatient = getSelectedPatient();

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: logout },
      ]
    );
  };

  const settingsOptions = [
    {
      section: 'Account',
      items: [
        {
          id: 'profile',
          title: 'Profile Information',
          subtitle: 'Update your personal details',
          icon: 'person-outline',
          onPress: () => {},
        },
        {
          id: 'patients',
          title: 'Manage Patients',
          subtitle: `${state.patients.length} patient${state.patients.length !== 1 ? 's' : ''} registered`,
          icon: 'people-outline',
          onPress: () => {},
        },
        {
          id: 'security',
          title: 'Security & Privacy',
          subtitle: 'Password, biometric settings',
          icon: 'shield-checkmark-outline',
          onPress: () => {},
        },
      ],
    },
    {
      section: 'Preferences',
      items: [
        {
          id: 'notifications',
          title: 'Notifications',
          subtitle: 'Appointment reminders, health tips',
          icon: 'notifications-outline',
          onPress: () => {},
        },
        {
          id: 'language',
          title: 'Language',
          subtitle: 'English',
          icon: 'language-outline',
          onPress: () => {},
        },
        {
          id: 'theme',
          title: 'Theme',
          subtitle: 'Light mode',
          icon: 'color-palette-outline',
          onPress: () => {},
        },
      ],
    },
    {
      section: 'Health Data',
      items: [
        {
          id: 'export',
          title: 'Export Health Data',
          subtitle: 'Download your medical records',
          icon: 'download-outline',
          onPress: () => {},
        },
        {
          id: 'sync',
          title: 'Data Sync',
          subtitle: 'Sync with health apps',
          icon: 'sync-outline',
          onPress: () => {},
        },
      ],
    },
    {
      section: 'Support',
      items: [
        {
          id: 'help',
          title: 'Help & Support',
          subtitle: 'FAQs, contact support',
          icon: 'help-circle-outline',
          onPress: () => {},
        },
        {
          id: 'feedback',
          title: 'Send Feedback',
          subtitle: 'Help us improve the app',
          icon: 'chatbubble-outline',
          onPress: () => {},
        },
        {
          id: 'about',
          title: 'About MediCare',
          subtitle: 'Version 1.0.0',
          icon: 'information-circle-outline',
          onPress: () => {},
        },
      ],
    },
  ];

  return (
    <SafeAreaView className="flex-1 bg-secondary-50">
      <View className="flex-1">
        {/* Header */}
        <View className="bg-white px-6 py-4 shadow-sm">
          <Text className="text-2xl font-bold text-secondary-800">Settings</Text>
          <Text className="text-secondary-500 mt-1">Manage your account and preferences</Text>
        </View>

        <ScrollView className="flex-1 px-6 py-6">
          {/* Current Patient Info */}
          {selectedPatient && (
            <Card variant="elevated" className="mb-6">
              <View className="flex-row items-center">
                <View className="h-16 w-16 rounded-full bg-primary-100 items-center justify-center mr-4">
                  <Ionicons 
                    name={selectedPatient.gender === 'female' ? 'woman' : 'man'} 
                    size={28} 
                    color="#1e40af" 
                  />
                </View>
                <View className="flex-1">
                  <Text className="text-lg font-semibold text-secondary-800">
                    {selectedPatient.name}
                  </Text>
                  <Text className="text-secondary-500">
                    {selectedPatient.age} years • {selectedPatient.gender}
                  </Text>
                  <Text className="text-sm text-primary-600 mt-1">
                    Currently selected patient
                  </Text>
                </View>
                <TouchableOpacity>
                  <Ionicons name="create-outline" size={20} color="#3b82f6" />
                </TouchableOpacity>
              </View>
            </Card>
          )}

          {/* Settings Sections */}
          {settingsOptions.map((section) => (
            <View key={section.section} className="mb-6">
              <Text className="text-lg font-bold text-secondary-800 mb-3 px-2">
                {section.section}
              </Text>
              
              <Card variant="elevated" padding="none">
                {section.items.map((item, index) => (
                  <TouchableOpacity
                    key={item.id}
                    onPress={item.onPress}
                    className={`
                      flex-row items-center p-4
                      ${index < section.items.length - 1 ? 'border-b border-secondary-100' : ''}
                    `}
                  >
                    <View className="h-10 w-10 rounded-full bg-secondary-100 items-center justify-center mr-4">
                      <Ionicons name={item.icon as any} size={20} color="#64748b" />
                    </View>
                    
                    <View className="flex-1">
                      <Text className="font-medium text-secondary-800">{item.title}</Text>
                      <Text className="text-sm text-secondary-500 mt-1">{item.subtitle}</Text>
                    </View>
                    
                    <Ionicons name="chevron-forward" size={16} color="#94a3b8" />
                  </TouchableOpacity>
                ))}
              </Card>
            </View>
          ))}

          {/* Emergency Contacts */}
          <View className="mb-6">
            <Text className="text-lg font-bold text-secondary-800 mb-3 px-2">
              Emergency
            </Text>
            
            <Card variant="elevated">
              <View className="flex-row items-center">
                <View className="h-12 w-12 rounded-full bg-error-100 items-center justify-center mr-4">
                  <Ionicons name="call" size={20} color="#ef4444" />
                </View>
                <View className="flex-1">
                  <Text className="font-medium text-secondary-800">Emergency Helpline</Text>
                  <Text className="text-sm text-secondary-500 mt-1">24/7 medical assistance</Text>
                </View>
                <TouchableOpacity className="bg-error-500 px-4 py-2 rounded-lg">
                  <Text className="text-white font-medium">Call 108</Text>
                </TouchableOpacity>
              </View>
            </Card>
          </View>

          {/* Logout Button */}
          <Card variant="outlined">
            <TouchableOpacity
              onPress={handleLogout}
              className="flex-row items-center justify-center py-2"
            >
              <Ionicons name="log-out-outline" size={20} color="#ef4444" />
              <Text className="text-error-500 font-medium ml-2">Logout</Text>
            </TouchableOpacity>
          </Card>

          {/* App Version */}
          <View className="items-center mt-8 mb-4">
            <Text className="text-secondary-400 text-sm">MediCare Patient Portal</Text>
            <Text className="text-secondary-400 text-sm">Version 1.0.0</Text>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default SettingsScreen;
