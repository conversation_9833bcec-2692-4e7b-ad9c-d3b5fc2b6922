import React, { useState, useRef, useEffect } from 'react';
import { View, Text, SafeAreaView, TextInput, Alert, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { useApp } from '../../context/AppContext';
import Button from '../../components/common/Button';

type OTPVerificationScreenNavigationProp = StackNavigationProp<RootStackParamList, 'OTPVerification'>;
type OTPVerificationScreenRouteProp = RouteProp<RootStackParamList, 'OTPVerification'>;

interface Props {
  navigation: OTPVerificationScreenNavigationProp;
  route: OTPVerificationScreenRouteProp;
}

const OTPVerificationScreen: React.FC<Props> = ({ navigation, route }) => {
  const { phone } = route.params;
  const { login } = useApp();
  const [otp, setOtp] = useState(['', '', '', '']);
  const [loading, setLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(30);
  const [canResend, setCanResend] = useState(false);
  const inputRefs = useRef<(TextInput | null)[]>([]);

  useEffect(() => {
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleOtpChange = (value: string, index: number) => {
    if (value.length > 1) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOTP = async () => {
    const otpString = otp.join('');
    
    if (otpString.length !== 4) {
      Alert.alert('Error', 'Please enter the complete OTP');
      return;
    }

    // For demo purposes, accept any 4-digit OTP
    if (otpString !== '1234' && otpString.length === 4) {
      // In real app, this would be validated against the server
    }

    setLoading(true);
    
    try {
      await login(phone);
      // Navigation will be handled by the AppNavigator based on auth state
    } catch (error) {
      Alert.alert('Error', 'Invalid OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (!canResend) return;

    setCanResend(false);
    setResendTimer(30);
    setOtp(['', '', '', '']);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    Alert.alert('Success', 'OTP has been resent to your mobile number');
    
    // Restart timer
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <LinearGradient
        colors={['#3b82f6', '#1e40af']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        className="flex-1"
      >
        <View className="flex-1 justify-center px-6">
          {/* Header */}
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="absolute top-12 left-6 z-10"
          >
            <View className="h-10 w-10 rounded-full bg-white/20 items-center justify-center">
              <Ionicons name="arrow-back" size={20} color="white" />
            </View>
          </TouchableOpacity>

          <View className="items-center mb-12">
            <View className="h-20 w-20 rounded-full bg-white/20 items-center justify-center mb-6">
              <Ionicons name="chatbubble-ellipses" size={40} color="white" />
            </View>
            
            <Text className="text-3xl font-bold text-white mb-2">Verify OTP</Text>
            <Text className="text-lg text-blue-100 text-center">
              Enter the 4-digit code sent to
            </Text>
            <Text className="text-lg text-white font-semibold">{phone}</Text>
          </View>

          <View className="bg-white rounded-2xl p-6 shadow-xl">
            <Text className="text-center text-secondary-600 mb-8">
              Enter OTP sent to your mobile
            </Text>

            {/* OTP Input */}
            <View className="flex-row justify-center mb-6">
              {otp.map((digit, index) => (
                <TextInput
                  key={index}
                  ref={(ref) => (inputRefs.current[index] = ref)}
                  value={digit}
                  onChangeText={(value) => handleOtpChange(value, index)}
                  onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                  className="w-14 h-14 border-2 border-secondary-200 rounded-xl text-center text-xl font-bold text-secondary-800 mx-2"
                  keyboardType="numeric"
                  maxLength={1}
                  selectTextOnFocus
                />
              ))}
            </View>

            {/* Resend OTP */}
            <View className="items-center mb-6">
              <Text className="text-secondary-500 text-sm">
                Didn't receive OTP?{' '}
                {canResend ? (
                  <TouchableOpacity onPress={handleResendOTP}>
                    <Text className="text-primary-600 font-semibold">Resend</Text>
                  </TouchableOpacity>
                ) : (
                  <Text className="text-secondary-400">
                    Resend in {resendTimer}s
                  </Text>
                )}
              </Text>
            </View>

            <Button
              title="Verify & Login"
              onPress={handleVerifyOTP}
              loading={loading}
              fullWidth
              size="large"
            />
          </View>

          <View className="mt-8 items-center">
            <Text className="text-blue-100 text-sm text-center">
              For demo purposes, use OTP: 1234{'\n'}
              Or enter any 4-digit code
            </Text>
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

export default OTPVerificationScreen;
