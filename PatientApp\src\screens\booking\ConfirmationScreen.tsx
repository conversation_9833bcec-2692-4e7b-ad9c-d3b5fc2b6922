import React from 'react';
import { View, Text, SafeAreaView, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { useApp } from '../../context/AppContext';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';

type ConfirmationScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Confirmation'>;
type ConfirmationScreenRouteProp = RouteProp<RootStackParamList, 'Confirmation'>;

interface Props {
  navigation: ConfirmationScreenNavigationProp;
  route: ConfirmationScreenRouteProp;
}

const ConfirmationScreen: React.FC<Props> = ({ navigation, route }) => {
  const { state } = useApp();
  
  // Get the latest appointment (just booked)
  const latestAppointment = state.appointments[state.appointments.length - 1];
  const doctor = state.doctors.find(d => d.id === latestAppointment?.doctorId);

  const handleDone = () => {
    navigation.navigate('Main', { screen: 'Home' });
  };

  const handleAddToCalendar = () => {
    // In a real app, this would integrate with the device calendar
    console.log('Add to calendar functionality');
  };

  return (
    <SafeAreaView className="flex-1 bg-secondary-50">
      <View className="flex-1">
        <ScrollView className="flex-1 px-6 py-12">
          {/* Success Icon */}
          <View className="items-center mb-8">
            <View className="h-24 w-24 rounded-full bg-success-100 items-center justify-center mb-6">
              <Ionicons name="checkmark" size={48} color="#10b981" />
            </View>
            
            <Text className="text-2xl font-bold text-secondary-800 mb-2 text-center">
              Appointment Confirmed!
            </Text>
            <Text className="text-secondary-500 text-center">
              Your appointment has been successfully booked.
            </Text>
          </View>

          {/* Appointment Details */}
          <Card variant="filled" className="bg-blue-50 border-blue-200 mb-6">
            <Text className="text-lg font-semibold text-secondary-800 mb-4">Appointment Details</Text>
            
            <View className="space-y-3">
              <View className="flex-row items-center">
                <Ionicons name="person-outline" size={20} color="#3b82f6" />
                <Text className="text-secondary-600 ml-3">{doctor?.name} ({doctor?.specialty})</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="calendar-outline" size={20} color="#3b82f6" />
                <Text className="text-secondary-600 ml-3">{latestAppointment?.date}</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="time-outline" size={20} color="#3b82f6" />
                <Text className="text-secondary-600 ml-3">{latestAppointment?.time}</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="card-outline" size={20} color="#3b82f6" />
                <Text className="text-secondary-600 ml-3">
                  Payment: ₹{latestAppointment?.amount} (Paid)
                </Text>
              </View>
            </View>
          </Card>

          {/* Next Steps */}
          <Card variant="elevated" className="mb-6">
            <Text className="text-lg font-semibold text-secondary-800 mb-4">What's Next?</Text>
            
            <View className="space-y-4">
              <View className="flex-row items-start">
                <View className="h-8 w-8 rounded-full bg-primary-100 items-center justify-center mr-3 mt-1">
                  <Text className="text-primary-600 font-bold text-sm">1</Text>
                </View>
                <View className="flex-1">
                  <Text className="font-medium text-secondary-800">Confirmation SMS</Text>
                  <Text className="text-sm text-secondary-500">
                    You'll receive a confirmation message with appointment details
                  </Text>
                </View>
              </View>
              
              <View className="flex-row items-start">
                <View className="h-8 w-8 rounded-full bg-primary-100 items-center justify-center mr-3 mt-1">
                  <Text className="text-primary-600 font-bold text-sm">2</Text>
                </View>
                <View className="flex-1">
                  <Text className="font-medium text-secondary-800">Reminder Notifications</Text>
                  <Text className="text-sm text-secondary-500">
                    We'll send you reminders before your appointment
                  </Text>
                </View>
              </View>
              
              <View className="flex-row items-start">
                <View className="h-8 w-8 rounded-full bg-primary-100 items-center justify-center mr-3 mt-1">
                  <Text className="text-primary-600 font-bold text-sm">3</Text>
                </View>
                <View className="flex-1">
                  <Text className="font-medium text-secondary-800">Visit the Doctor</Text>
                  <Text className="text-sm text-secondary-500">
                    Arrive 15 minutes early with your ID and medical history
                  </Text>
                </View>
              </View>
            </View>
          </Card>

          {/* Action Buttons */}
          <View className="space-y-3">
            <Button
              title="Add to Calendar"
              onPress={handleAddToCalendar}
              variant="outline"
              fullWidth
              size="large"
              icon={<Ionicons name="calendar-outline" size={20} color="#3b82f6" />}
            />
            
            <Button
              title="Done"
              onPress={handleDone}
              fullWidth
              size="large"
            />
          </View>

          {/* Support */}
          <Card variant="outlined" className="mt-6">
            <View className="flex-row items-center">
              <View className="h-12 w-12 rounded-full bg-secondary-100 items-center justify-center mr-4">
                <Ionicons name="help-circle-outline" size={24} color="#64748b" />
              </View>
              <View className="flex-1">
                <Text className="font-medium text-secondary-800">Need Help?</Text>
                <Text className="text-sm text-secondary-500">
                  Contact our support team for any assistance
                </Text>
              </View>
              <Button
                title="Contact"
                variant="ghost"
                onPress={() => {}}
              />
            </View>
          </Card>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default ConfirmationScreen;
